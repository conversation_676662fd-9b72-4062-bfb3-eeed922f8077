# Compiled class file
*.class

# Log file
*.log

# BlueJ files
*.ctxt

# Mobile Tools for Java (J2ME)
.mtj.tmp/

# Package Files #
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# virtual machine crash logs, see http://www.java.com/en/download/help/error_hotspot.xml
hs_err_pid*

# IntelliJ
.idea/
*/.idea/
*/.idea/
*.iml
*.iml
*.iws
*.iml
*.ipr
*.ips


# Eclipse
.settings/
.metadata/
.classpath
.project


# Generated
target/
/target/
*/target/*
*/*/target/*
javadoc/
bin/
.bin
/*/bin/
classes/

# Misc.
.DS_Store
**/**/.DS_Store
**/*/.DS_Store

*.jar
*.war

.metadata/*
*/.svn/*
*.log
*.project
*.classpath
**/.classpath

out/*
target/
.settings
/*/.settings/
*~
.vscode/
.cursor/rules
.cursorindexingignore
# SpecStory explanation file
.specstory/.what-is-this.md
.specstory/