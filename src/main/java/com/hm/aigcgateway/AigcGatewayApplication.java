package com.hm.aigcgateway;

import java.time.LocalDateTime;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * Spring Cloud Gateway 应用主类
 */
@SpringBootApplication
@EnableDiscoveryClient
public class AigcGatewayApplication {

    public static void main(String[] args) {
        System.out.println(now() + " AigcGatewayApplication starting...");
        SpringApplication.run(AigcGatewayApplication.class, args);
        System.out.println(now() + " AigcGatewayApplication started");
    }

    private static String now() {
        return LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
    
} 