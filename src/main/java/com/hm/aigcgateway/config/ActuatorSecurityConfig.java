package com.hm.aigcgateway.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity;
import org.springframework.security.config.web.server.ServerHttpSecurity;
import org.springframework.security.web.server.SecurityWebFilterChain;

@Configuration
@EnableWebFluxSecurity
public class ActuatorSecurityConfig {

    @Bean
    public SecurityWebFilterChain securityWebFilterChain(ServerHttpSecurity http) {
        http
            .authorizeExchange()
                // 允许健康检查和info端点匿名访问
                .pathMatchers("/actuator/health", "/actuator/info").permitAll()
                // prometheus端点需要认证
                .pathMatchers("/actuator/prometheus").authenticated()
                // 其他请求可根据需要配置
                .anyExchange().permitAll()
            .and()
                .httpBasic()
            .and()
                .csrf().disable();
        return http.build();
    }
}