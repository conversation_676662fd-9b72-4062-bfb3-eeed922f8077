package com.hm.aigcgateway.config;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.env.EnvironmentPostProcessor;
import org.springframework.core.Ordered;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.PropertiesPropertySource;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.support.PropertiesLoaderUtils;

public class ExternalPropertiesPostProcessor implements EnvironmentPostProcessor, Ordered {

    @Override
    public void postProcessEnvironment(ConfigurableEnvironment environment, SpringApplication application) {
        try {
            PropertiesPropertySource commonProps = new PropertiesPropertySource(
                "commonLoggingProps", 
                PropertiesLoaderUtils.loadProperties(
                    new FileSystemResource("/hm/config/java/aigc-gateway-common.properties")
                )
            );
            System.out.println("loaded common config:" + commonProps);
            environment.getPropertySources().addFirst(commonProps);

            PropertiesPropertySource customProps = new PropertiesPropertySource(
                "customLoggingProps", 
                PropertiesLoaderUtils.loadProperties(
                    new FileSystemResource("/hm/config/java/aigc-gateway-custom.properties")
                )
            );
            System.out.println("loaded custom config:" + customProps);
            environment.getPropertySources().addFirst(customProps);
        } catch (Exception e) {
            // 处理异常，但不要打日志（日志还未初始化）
            System.err.println("Failed to load early logging properties: ");
            e.printStackTrace();
        }
    }
    
    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE;
    }

}