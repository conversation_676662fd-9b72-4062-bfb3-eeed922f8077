package com.hm.aigcgateway.config;

import org.springframework.context.annotation.Configuration;

import lombok.extern.slf4j.Slf4j;

/**
 * 网关配置类
 */
@Configuration
@Slf4j
public class GatewayConfig {

    // @Bean
    // public SseResponseBodyRewrite sseResponseBodyRewrite() {
    //     return new SseResponseBodyRewrite();
    // }
    
    // @Bean
    // public static PropertySourcesPlaceholderConfigurer propertySourcesPlaceholderConfigurer() {
    //     PropertySourcesPlaceholderConfigurer configurer = new PropertySourcesPlaceholderConfigurer();
    //     configurer.setIgnoreResourceNotFound(true);
    //     configurer.setLocalOverride(true); // 让本地属性源覆盖系统属性
        
    //     // 手动设置属性源顺序
    //     Resource[] resources = new Resource[] {
    //         new FileSystemResource("/hm/config/java/aigc-gateway-common.properties"),
    //         new FileSystemResource("/hm/config/java/aigc-gateway-custom.properties")
    //     };
    //     configurer.setLocations(resources);
        
    //     return configurer;
    // }
    
} 