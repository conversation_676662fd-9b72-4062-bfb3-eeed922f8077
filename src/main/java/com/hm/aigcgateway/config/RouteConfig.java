// package com.hm.aigcgateway.config;

// import org.springframework.cloud.gateway.route.RouteLocator;
// import org.springframework.cloud.gateway.route.builder.RouteLocatorBuilder;
// import org.springframework.context.annotation.Bean;
// import org.springframework.context.annotation.Configuration;
// import org.springframework.http.HttpHeaders;

// import com.hm.aigcgateway.filter.SseResponseBodyRewrite;

// import lombok.extern.slf4j.Slf4j;

// /**
//  * 路由配置类
//  * 除了在application.yml中配置路由外，也可以通过此类进行代码方式的路由配置
//  */
// @Configuration
// @Slf4j
// public class RouteConfig {

//     /**
//      * 提供SSE路由，确保SSE路由处理正确
//      */
//     @Bean
//     public RouteLocator customSseRoutes(RouteLocatorBuilder builder, SseResponseBodyRewrite rewriter) {
//         log.info("配置额外SSE路由");
        
//         return builder.routes()
//             // SSE直接路由
//             .route("manual-aigc-sse-route", r -> r
//                 .path("/api/aigc/sse/**")
//                 .filters(f -> f
//                     .stripPrefix(3)
//                     .modifyResponseBody(String.class, String.class, rewriter)
//                     .removeRequestHeader("Connection")
//                     .removeResponseHeader("Content-Length")
//                     .removeResponseHeader(HttpHeaders.TRANSFER_ENCODING))
//                 .uri("lb://aigc-service"))
//             // 聊天路由
//             .route("manual-aigc-chat-route", r -> r
//                 .path("/api/aigc/chat/**")
//                 .filters(f -> f
//                     .stripPrefix(3)
//                     .modifyResponseBody(String.class, String.class, rewriter)
//                     .circuitBreaker(config -> config
//                         .setName("aigcChatCircuitBreaker")
//                         .setFallbackUri("forward:/fallback"))
//                     .setRequestSize(5000000L)
//                     .addRequestHeader("X-Custom-aigcChat-Header", "AigcChatValue")
//                     .removeRequestHeader("Connection")
//                     .removeResponseHeader("Content-Length")
//                     .removeResponseHeader(HttpHeaders.TRANSFER_ENCODING)
//                     .dedupeResponseHeader("Access-Control-Allow-Origin", "RETAIN_FIRST"))
//                 .uri("lb://aigc-chat-service"))
//             .build();
//     }

// } 