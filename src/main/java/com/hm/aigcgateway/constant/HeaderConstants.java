package com.hm.aigcgateway.constant;

/**
 * HTTP请求头常量类
 */
public class HeaderConstants {
    
    /**
     * 网关请求ID头
     */
    public static final String X_GW_REQUEST_ID = "X-GWRequest-ID";
    
    /**
     * 网关请求开始时间头
     */
    public static final String X_GW_REQUEST_START_TIME = "X-GWRequest-ST";

    /**
     * 下游服务不可用
     */
    public static final String HM_RESPONSE_HEADER_NOT_AVAILABLE = "hmrh-na";

    /**
     * 下游服务返回第一个token的时间
     */
    public static final String HM_RESPONSE_FIRST_TOKEN_START_TIME = "hmrh-ft-startTime";
    
    
    private HeaderConstants() {
        // 私有构造函数，防止实例化
    }
    
} 