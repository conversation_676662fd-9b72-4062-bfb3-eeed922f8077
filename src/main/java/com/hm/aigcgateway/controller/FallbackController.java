package com.hm.aigcgateway.controller;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeoutException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.support.NotFoundException;
import org.springframework.http.HttpMethod;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ServerWebExchange;

import com.hm.aigcgateway.metrics.NotAvailableMetrics;
import com.hm.aigcgateway.util.ServerWebExchangeUtils;

import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * 服务降级处理控制器
 */
@RestController
@Slf4j
public class FallbackController {

    @Autowired
    private NotAvailableMetrics notAvailableMetrics;
    
    /**
     * 默认降级处理
     * @return 降级响应
     */
    @GetMapping("/fallback")
    public Mono<Map<String, Object>> fallback(ServerWebExchange exchange) {
        // String path = exchange.getRequest().getURI().getPath();
        String path = ServerWebExchangeUtils.extractApiOriginalPath(exchange);
        HttpMethod method = exchange.getRequest().getMethod();

        //example:
        /**
         * (1).
         *  2025-06-10 11:15:39.784 [parallel-12] ERROR com.hm.aigcgateway.controller.FallbackController - exchange.getAttributes:
         *  {org.springframework.web.reactive.HandlerMapping.bestMatchingHandler=com.hm.aigcgateway.controller.
         * FallbackController#fallback(ServerWebExchange), org.springframework.cloud.gateway.support.
         * ServerWebExchangeUtils.gatewayOriginalRequestUrl=[http://*************:8580/api/aigc/chat/test-sse, lb://backendA-service/test-sse], 
         * org.springframework.web.reactive.HandlerMapping.bestMatchingPattern=/fallback, org.springframework.cloud.gateway.support.
         * ServerWebExchangeUtils.circuitBreakerExecutionException=java.util.concurrent.TimeoutException: Did not observe any item or terminal signal 
         * within 1000ms in 'circuitBreaker' (and no fallback has been configured), org.springframework.cloud.gateway.support
         * .ServerWebExchangeUtils.gatewayHandlerMapper=RoutePredicateHandlerMapping, org.springframework.cloud.gateway.support
         * .ServerWebExchangeUtils.gatewayRequestUrl=/fallback, org.springframework.cloud.gateway.support
         * .ServerWebExchangeUtils.uriTemplateVariables={}, org.springframework.cloud.gateway.support
         * .ServerWebExchangeUtils.routeWeight={}, org.springframework.web.reactive.HandlerMapping.uriTemplateVariables={}, 
         * org.springframework.web.server.ServerWebExchange.LOG_ID=12508245-5, org.springframework.web.reactive.HandlerMapping.matrixVariables={}, 
         * org.springframework.cloud.gateway.support.ServerWebExchangeUtils.gatewayRoute=Route{id='manual-aigc-chat-route', 
         * uri=lb://backendA-service, order=0, predicate=Paths: [/api/aigc/chat/**], match trailing slash: true, 
         * gatewayFilters=[[[StripPrefix parts = 3], order = 0], [ModifyResponseBody New content type = [null], 
         * In class = String, Out class = String], [[SpringCloudCircuitBreakerResilience4JFilterFactory name = 'aigcChatCircuitBreaker', 
         * fallback = forward:/fallback], order = 0], [[RequestSize max = 5000000B], order = 0], 
         * [[AddRequestHeader X-Custom-aigcChat-Header = 'AigcChatValue'], order = 0], [[RemoveRequestHeader name = 'Connection'], order = 0], 
         * [[RemoveResponseHeader name = 'Content-Length'], order = 0], [[RemoveResponseHeader name = 'Transfer-Encoding'], order = 0],
         *  [[DedupeResponseHeader Access-Control-Allow-Origin = RETAIN_FIRST], order = 0]], metadata={}}}
         * 
         * (2).
         * 2025-06-13 15:54:21.459 [reactor-http-nio-6] ERROR [gw-7b23b6ae-6621-4ecc-bb40-184bfcf29d5f] 
         * com.hm.aigcgateway.controller.FallbackController - 服务降级触发: GET /api/aigc/chat/sse, 请求头: {Host=[*************:8580], 
         * Cache-Control=[max-age=0], Upgrade-Insecure-Requests=[1], User-Agent=[Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) 
         * AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36], Accept=[text/html,application/xhtml+xml,application/xml;
         * q=0.9,image/avif,image/webp,image/apng,*;q=0.8,application/signed-exchange;v=b3;q=0.7], Accept-Encoding=[gzip, deflate], 
         Accept-Language=[zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7], X-GWRequest-ID=[gw-7b23b6ae-6621-4ecc-bb40-184bfcf29d5f]}, 
         exchange.getAttributes: {y_attr_request_start_time=1749801261429, org.springframework.web.reactive.HandlerMapping.
         bestMatchingHandler=com.hm.aigcgateway.controller.FallbackController#fallback(ServerWebExchange), 
         org.springframework.cloud.gateway.support.ServerWebExchangeUtils.gatewayOriginalRequestUrl=[http://*************:8580/api/aigc/chat/sse, 
         lb://backendA-service/sse], org.springframework.web.reactive.HandlerMapping.bestMatchingPattern=/fallback, org.springframework.cloud.gateway.
         support.ServerWebExchangeUtils.circuitBreakerExecutionException=
         org.springframework.cloud.gateway.support.NotFoundException: 503 SERVICE_UNAVAILABLE "Unable to find instance for backendA-service", 
         org.springframework.cloud.gateway.support.ServerWebExchangeUtils.gatewayHandlerMapper=RoutePredicateHandlerMapping, 
         y_attr_request_id=gw-7b23b6ae-6621-4ecc-bb40-184bfcf29d5f, 
         org.springframework.cloud.gateway.support.ServerWebExchangeUtils.gatewayRequestUrl=/fallback, 
         org.springframework.cloud.gateway.support.ServerWebExchangeUtils.uriTemplateVariables={}, 
         org.springframework.cloud.gateway.support.ServerWebExchangeUtils.routeWeight={}, 
         org.springframework.web.reactive.HandlerMapping.uriTemplateVariables={}, 
         org.springframework.web.server.ServerWebExchange.LOG_ID=bd404eae-3, 
         org.springframework.web.reactive.HandlerMapping.matrixVariables={}, 
         org.springframework.cloud.gateway.support.ServerWebExchangeUtils.gatewayRoute=Route{id='aigc-sse-service', uri=lb://backendA-service, order=0, 
         predicate=Paths: [/api/aigc/chat/**], match trailing slash: true, gatewayFilters=[[[StripPrefix parts = 3], order = 1], 
         [com.hm.aigcgateway.filter.SseResponseHeaderGatewayFilterFactory$$Lambda$849/1639177819@7ad50403, order = 2], 
         [com.hm.aigcgateway.filter.NonBufferingGatewayFilterFactory$$Lambda$850/289094121@78e4c68, order = 3], 
         [[SpringCloudCircuitBreakerResilience4JFilterFactory name = 'aigcChatCircuitBreaker', 
         fallback = forward:/fallback], order = 4], [[DedupeResponseHeader Access-Control-Allow-Origin = RETAIN_FIRST], order = 5], 
         [[RemoveResponseHeader name = 'Content-Length'], order = 6], [[RemoveResponseHeader name = 'Transfer-Encoding'], order = 7], 
         [[RemoveRequestHeader name = 'Connection'], order = 8], [[RequestSize max = 5000000B], order = 9]], metadata={}}}

         (3).
         2025-06-17 16:57:20.031 [reactor-http-nio-5] ERROR [] com.hm.aigcgateway.controller.FallbackController - 
         服务降级触发: GET /api/aigc/recommend/api/hello, 请求头: {Host=[localhost:8580], Connection=[keep-alive], Cache-Control=[max-age=0], 
         sec-ch-ua=["Google Chrome";v="125", "Chromium";v="125", "Not.A/Brand";v="24"], sec-ch-ua-mobile=[?0], sec-ch-ua-platform=["macOS"], 
         Upgrade-Insecure-Requests=[1], User-Agent=[Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) 
         Chrome/********* Safari/537.36], Accept=[text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*;
         q=0.8,application/signed-exchange;v=b3;q=0.7], Sec-Fetch-Site=[none], Sec-Fetch-Mode=[navigate], Sec-Fetch-User=[?1], 
         Sec-Fetch-Dest=[document], Accept-Encoding=[gzip, deflate, br, zstd], Accept-Language=[zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7], 
         Cookie=[Idea-97570e4d=63b2dcbb-fff0-4374-8b88-46837242cc06], X-GWRequest-ID=[gw-247c61e3-970e-4202-955f-2b12159d36f2], 
         X-GWRequest-ST=[1750150639885]}, exchange.getAttributes: {y_attr_request_start_time=1750150639885, 
         org.springframework.web.reactive.HandlerMapping.bestMatchingHandler=com.hm.aigcgateway.controller.
         FallbackController#fallback(ServerWebExchange), org.springframework.cloud.gateway.support.ServerWebExchangeUtils.
         gatewayOriginalRequestUrl=[http://localhost:8580/api/aigc/recommend/api/hello, lb://backendA-service/api/hello], 
         y_attr_api_original_path=/api/aigc/recommend/api/hello, 
         org.springframework.web.reactive.HandlerMapping.bestMatchingPattern=/fallback, org.springframework.
         cloud.gateway.support.ServerWebExchangeUtils.circuitBreakerExecutionException=java.io.IOException: Connection reset by peer, 
         org.springframework.cloud.gateway.support.ServerWebExchangeUtils.gatewayHandlerMapper=RoutePredicateHandlerMapping, 
         y_attr_request_id=gw-247c61e3-970e-4202-955f-2b12159d36f2, org.springframework.cloud.gateway.support.
         ServerWebExchangeUtils.gatewayRequestUrl=/fallback, org.springframework.cloud.gateway.support.
         ServerWebExchangeUtils.uriTemplateVariables={}, org.springframework.cloud.gateway.support.ServerWebExchangeUtils.routeWeight={}, 
         org.springframework.web.reactive.HandlerMapping.uriTemplateVariables={}, org.springframework.web.server.
         ServerWebExchange.LOG_ID=98ba8829-7, org.springframework.web.reactive.HandlerMapping.matrixVariables={}, 
         org.springframework.cloud.gateway.support.ServerWebExchangeUtils.gatewayRoute=Route{id='aigc-service', 
         uri=lb://backendA-service, order=0, predicate=Paths: [/api/aigc/recommend/**], match trailing slash: true, 
         gatewayFilters=[[[StripPrefix parts = 3], order = 1], [[SpringCloudCircuitBreakerResilience4JFilterFactory name = 
         'aigcServiceCircuitBreaker', fallback = forward:/fallback], order = 2]], metadata={}}}
         */
        log.error("服务降级触发: {} {}, 请求头: {}, exchange.getAttributes: {}", 
            method != null ? method.name() : "UNKNOWN", path, exchange.getRequest().getHeaders(), exchange.getAttributes());
  
        Throwable circuitBreakerExecutionException = exchange.getAttribute("org.springframework.cloud.gateway.support.ServerWebExchangeUtils.circuitBreakerExecutionException");
        if (circuitBreakerExecutionException != null) {
            if (circuitBreakerExecutionException instanceof TimeoutException) {
                notAvailableMetrics.recordError(path, "timeout");
            }
            else if (circuitBreakerExecutionException instanceof NotFoundException) {
                notAvailableMetrics.recordError(path, "serviceUnavailable503");
            }
            else {
                notAvailableMetrics.recordError(path, "other");
            }
        }
        Map<String, Object> response = new HashMap<>();
        response.put("code", 503);
        response.put("message", "服务暂时不可用，请稍后再试-Service Unavailable");
        response.put("path", path);
        response.put("timestamp", System.currentTimeMillis());
        return Mono.just(response);
    }
    
} 