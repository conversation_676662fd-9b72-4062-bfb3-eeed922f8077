// package com.hm.aigcgateway.controller;

// import java.util.HashMap;
// import java.util.List;
// import java.util.Map;
// import java.util.stream.Collectors;

// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.cloud.client.ServiceInstance;
// import org.springframework.cloud.client.discovery.DiscoveryClient;
// import org.springframework.http.server.reactive.ServerHttpRequest;
// import org.springframework.web.bind.annotation.GetMapping;
// import org.springframework.web.bind.annotation.RestController;

// import lombok.extern.slf4j.Slf4j;
// import reactor.core.publisher.Mono;


// @RestController
// @Slf4j
// public class ServiceDebugController {

//     @Autowired
//     private DiscoveryClient discoveryClient;
    
//     /**
//      * 检查网关状态
//      */
//     @GetMapping("/gateway/status")
//     public Mono<Map<String, Object>> gatewayStatus(ServerHttpRequest request) {
//         Map<String, Object> result = new HashMap<>();
//         result.put("status", "UP");
//         result.put("timestamp", System.currentTimeMillis());
//         result.put("path", request.getURI().getPath());
        
//         // 收集已注册的服务信息
//         List<String> services = discoveryClient.getServices();
//         Map<String, List<String>> serviceDetails = services.stream()
//             .collect(Collectors.toMap(
//                 serviceName -> serviceName,
//                 serviceName -> discoveryClient.getInstances(serviceName).stream()
//                     .map(ServiceInstance::getUri)
//                     .map(Object::toString)
//                     .collect(Collectors.toList())
//             ));
        
//         result.put("services", serviceDetails);
//         return Mono.just(result);
//     }

// }