// package com.hm.aigcgateway.filter;

// import org.slf4j.MDC;
// import org.springframework.cloud.gateway.filter.GatewayFilter;
// import org.springframework.cloud.gateway.filter.GatewayFilterChain;
// import org.springframework.core.Ordered;
// import org.springframework.http.server.reactive.ServerHttpRequest;
// import org.springframework.http.server.reactive.ServerHttpResponse;
// import org.springframework.stereotype.Component;
// import org.springframework.web.server.ServerWebExchange;

// import com.hm.aigcgateway.constant.HeaderConstants;

// import lombok.extern.slf4j.Slf4j;
// import reactor.core.publisher.Mono;

// @Component
// @Slf4j
// public class AddRequestIDHeaderFilter implements GatewayFilter, Ordered {

//     @Override
//     public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
//         ServerHttpRequest request = exchange.getRequest();
//         ServerHttpResponse response = exchange.getResponse();
//         String requestId = null;
//         if (request.getHeaders().containsKey(HeaderConstants.X_GW_REQUEST_ID)) {
//             requestId = request.getHeaders().get(HeaderConstants.X_GW_REQUEST_ID).get(0);
//             MDC.put(HeaderConstants.X_GW_REQUEST_ID, requestId);
//             response.getHeaders().add(HeaderConstants.X_GW_REQUEST_ID, requestId);
//         }
//         log.trace("add response header {}: {}", HeaderConstants.X_GW_REQUEST_ID, requestId);
//         return chain.filter(exchange);
//     }

//     @Override
//     public int getOrder() {
//         return 0;
//     }

// }
