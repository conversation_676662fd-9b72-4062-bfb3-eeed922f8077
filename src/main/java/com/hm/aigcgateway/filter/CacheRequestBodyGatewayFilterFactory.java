// package com.hm.aigcgateway.filter;

// import org.apache.logging.log4j.LogManager;
// import org.apache.logging.log4j.Logger;
// import org.springframework.cloud.gateway.filter.GatewayFilter;
// import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
// import org.springframework.cloud.gateway.filter.factory.rewrite.CachedBodyOutputMessage;
// import org.springframework.cloud.gateway.support.BodyInserterContext;
// import org.springframework.core.io.buffer.DataBuffer;
// import org.springframework.core.io.buffer.DataBufferUtils;
// import org.springframework.http.HttpHeaders;
// import org.springframework.http.MediaType;
// import org.springframework.http.ReactiveHttpOutputMessage;
// import org.springframework.http.server.reactive.ServerHttpRequest;
// import org.springframework.http.server.reactive.ServerHttpRequestDecorator;
// import org.springframework.stereotype.Component;
// import org.springframework.web.reactive.function.BodyInserter;
// import org.springframework.web.reactive.function.BodyInserters;
// import org.springframework.web.reactive.function.server.HandlerStrategies;
// import org.springframework.web.reactive.function.server.ServerRequest;
// import org.springframework.web.server.ServerWebExchange;
// import reactor.core.publisher.Flux;
// import reactor.core.publisher.Mono;

// /**
//  * 请求体缓存过滤器工厂
//  * 用于缓存请求体，以便在后续过滤器中重复使用
//  */
// @Component
// public class CacheRequestBodyGatewayFilterFactory extends AbstractGatewayFilterFactory<CacheRequestBodyGatewayFilterFactory.Config> {

//     private static final Logger logger = LogManager.getLogger(CacheRequestBodyGatewayFilterFactory.class);

//     public CacheRequestBodyGatewayFilterFactory() {
//         super(Config.class);
//     }

//     @Override
//     public GatewayFilter apply(Config config) {
//         return (exchange, chain) -> {
//             ServerRequest serverRequest = ServerRequest.create(exchange, HandlerStrategies.withDefaults().messageReaders());
            
//             // 只缓存POST、PUT等有请求体的请求
//             MediaType mediaType = exchange.getRequest().getHeaders().getContentType();
//             if (mediaType == null || !shouldCacheBody(exchange.getRequest())) {
//                 return chain.filter(exchange);
//             }
            
//             return serverRequest.bodyToMono(String.class)
//                 .switchIfEmpty(Mono.just(""))
//                 .flatMap(body -> {
//                     if (body.isEmpty()) {
//                         return chain.filter(exchange);
//                     }
                    
//                     logger.debug("缓存请求体: {} 字节", body.length());
                    
//                     // 缓存原始请求体
//                     exchange.getAttributes().put("cachedRequestBody", body);
                    
//                     // 重新包装请求
//                     ServerHttpRequest mutatedRequest = getRequestWithCachedBody(exchange, body);
                    
//                     return chain.filter(exchange.mutate().request(mutatedRequest).build());
//                 });
//         };
//     }
    
//     private boolean shouldCacheBody(ServerHttpRequest request) {
//         String method = request.getMethodValue();
//         return "POST".equals(method) || "PUT".equals(method) || "PATCH".equals(method);
//     }
    
//     private ServerHttpRequest getRequestWithCachedBody(ServerWebExchange exchange, String body) {
//         ServerHttpRequest request = exchange.getRequest();
        
//         ServerHttpRequestDecorator decorator = new ServerHttpRequestDecorator(request) {
//             @Override
//             public Flux<DataBuffer> getBody() {
//                 if (body.isEmpty()) {
//                     return Flux.empty();
//                 }
                
//                 HttpHeaders headers = new HttpHeaders();
//                 headers.putAll(request.getHeaders());
                
//                 BodyInserter<String, ReactiveHttpOutputMessage> bodyInserter = BodyInserters.fromValue(body);
//                 CachedBodyOutputMessage outputMessage = new CachedBodyOutputMessage(exchange, headers);
                
//                 return bodyInserter.insert(outputMessage, new BodyInserterContext())
//                     .then(Mono.defer(() -> {
//                         return Flux.defer(() -> {
//                             return outputMessage.getBody();
//                         });
//                     }))
//                     .doOnError(throwable -> {
//                         DataBufferUtils.release(outputMessage.getBody());
//                     })
//                     .flatMapMany(dataBuffer -> outputMessage.getBody());
//             }
//         };
        
//         return decorator;
//     }
    
//     // 配置类，目前为空配置
//     public static class Config {
//         // 可以添加配置参数
//     }
// } 