package com.hm.aigcgateway.filter;

import java.util.Arrays;
import java.util.List;

import org.slf4j.MDC;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;

import com.hm.aigcgateway.constant.HeaderConstants;
import com.hm.aigcgateway.util.ServerWebExchangeUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 禁用响应缓冲过滤器工厂
 * 确保SSE响应不被缓冲，实现真正的流式传输
 */
@Component
@Slf4j
public class NonBufferingGatewayFilterFactory
        extends AbstractGatewayFilterFactory<NonBufferingGatewayFilterFactory.Config> {

    public NonBufferingGatewayFilterFactory() {
        super(Config.class);
    }

    @Override
    public List<String> shortcutFieldOrder() {
        return Arrays.asList("enabled");
    }

    @Override
    public GatewayFilter apply(Config config) {
        return (exchange, chain) -> {
            if (!config.isEnabled()) {
                return chain.filter(exchange);
            }

            String requestId = ServerWebExchangeUtils.getAttrRequestId(exchange);
            Long startTime = ServerWebExchangeUtils.getAttrRequestStartTime(exchange);
            String path = ServerWebExchangeUtils.getAtrrApiOriginalPath(exchange);
            MDC.put(HeaderConstants.X_GW_REQUEST_ID, requestId);
            log.debug("准备禁用响应缓冲过滤器处理请求: path:{}, requestId:{}, startTime:{}", path, requestId, startTime);
            MDC.remove(HeaderConstants.X_GW_REQUEST_ID);
            // 创建一个具有特殊属性的交换对象，标记为不缓冲
            ServerWebExchange exchangeWithoutBuffering = exchange.mutate()
                    .response(new NonBufferingServerHttpResponseDecorator(exchange.getResponse()))
                    .build();

            // 禁用NettyWriteResponseFilter的聚合行为
            exchangeWithoutBuffering.getAttributes().put("original_response_content_type",
                    MediaType.TEXT_EVENT_STREAM_VALUE);

            return chain.filter(exchangeWithoutBuffering)
                    .doOnSuccess(v -> {
                        MDC.put(HeaderConstants.X_GW_REQUEST_ID, requestId);
                        log.debug("已禁用响应缓冲过滤器处理请求: {}, requestId:{}, startTime:{}", path, requestId, startTime);
                        MDC.remove(HeaderConstants.X_GW_REQUEST_ID);
                    });
        };
    }

    public static class Config {
        private boolean enabled = true;

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }
    }

}