package com.hm.aigcgateway.filter;

import org.reactivestreams.Publisher;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.http.server.reactive.ServerHttpResponseDecorator;

import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 非缓冲响应装饰器
 * 重写writeWith和writeAndFlushWith方法，直接传递流数据而不进行缓冲
 */
@Slf4j
public class NonBufferingServerHttpResponseDecorator extends ServerHttpResponseDecorator {

    public NonBufferingServerHttpResponseDecorator(ServerHttpResponse delegate) {
        super(delegate);
    }

    @Override
    public Mono<Void> writeWith(Publisher<? extends DataBuffer> body) {
        log.debug("NonBufferingServerHttpResponseDecorator writeWith");
        // 直接传递流，不做任何缓冲
        if (body instanceof Flux) {
            return getDelegate().writeAndFlushWith(
                    Flux.from(body).map(dataBuffer -> Flux.just(dataBuffer))
            );
        }
        return getDelegate().writeWith(body);
    }

    @Override
    public Mono<Void> writeAndFlushWith(Publisher<? extends Publisher<? extends DataBuffer>> body) {
        log.debug("NonBufferingServerHttpResponseDecorator writeAndFlushWith");
        // 直接传递，禁用任何聚合
        return getDelegate().writeAndFlushWith(body);
    }
    
} 