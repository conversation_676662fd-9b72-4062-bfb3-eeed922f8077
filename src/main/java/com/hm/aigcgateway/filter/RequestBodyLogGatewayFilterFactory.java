// package com.hm.aigcgateway.filter;

// import java.net.URI;
// import org.springframework.cloud.gateway.filter.GatewayFilter;
// import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
// import org.springframework.cloud.gateway.route.Route;
// import org.springframework.cloud.gateway.support.ServerWebExchangeUtils;
// import org.springframework.http.server.reactive.ServerHttpRequest;
// import org.springframework.stereotype.Component;
// import lombok.extern.slf4j.Slf4j;

// @Slf4j
// @Component
// public class RequestBodyLogGatewayFilterFactory
//     extends AbstractGatewayFilterFactory<RequestBodyLogGatewayFilterFactory.Config> {
    
//     public RequestBodyLogGatewayFilterFactory() {
//         super(Config.class);
//         log.info("RequestBodyLogGatewayFilterFactory inited");
//     }
 
//     @Override
//     public GatewayFilter apply(Config config) {
//         return (exchange, chain) -> {
//             Route gatewayUrl = exchange.getRequiredAttribute(ServerWebExchangeUtils.GATEWAY_ROUTE_ATTR);
//             URI uri = gatewayUrl.getUri();
//             String instance = uri.getAuthority();
//             ServerHttpRequest request = exchange.getRequest();
//             String URIPath = request.getURI().toString();
//             String method = request.getMethodValue();
//             log.info("RequestBodyLogGatewayFilterFactory: method={}, uri={}, instance={}", method, URIPath, instance);
//             return chain.filter(exchange);
//         };
//     }
 
//     public static class Config {

//     }
    
// }
