package com.hm.aigcgateway.filter;

import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;

import com.hm.aigcgateway.constant.HeaderConstants;
import com.hm.aigcgateway.metrics.NonSseMetrics;
import com.hm.aigcgateway.metrics.NotAvailableMetrics;
import com.hm.aigcgateway.util.ServerWebExchangeUtils;
import com.hm.aigcgateway.util.URLUtils;

import reactor.core.publisher.Mono;

/**
 * 全局过滤器
 * 用于请求日志记录和请求/响应处理
 */
@Component
public class RequestGlobalFilter implements GlobalFilter, Ordered {

    private static final Logger logger = LoggerFactory.getLogger(RequestGlobalFilter.class);

    @Autowired
    private NonSseMetrics nonSseMetrics;

    @Autowired
    private NotAvailableMetrics notAvailableMetrics;

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        Long startTime = System.currentTimeMillis();
        String requestId = "gw-" + UUID.randomUUID().toString();
        ServerHttpRequest request = exchange.getRequest();
        String path = request.getURI().getPath();
        logger.info("Request {} started, {} {}, startTime:{}", requestId, request.getMethod(),
                path, startTime);
                
        // 添加请求头
        ServerHttpRequest newRequest = request.mutate()
                .header(HeaderConstants.X_GW_REQUEST_ID, requestId)
                .header(HeaderConstants.X_GW_REQUEST_START_TIME, String.valueOf(startTime))
                .build();

        // 设置MDC并记录请求信息（同步执行）
        MDC.put(HeaderConstants.X_GW_REQUEST_ID, requestId);

        ServerWebExchange newExchange = exchange.mutate()
                .request(newRequest)
                .build();

        ServerWebExchangeUtils.setAttrRequestId(newExchange, requestId);
        ServerWebExchangeUtils.setAttrRequestStartTime(newExchange, startTime);
        ServerWebExchangeUtils.setAtrrApiOriginalPath(newExchange, path);

        return chain.filter(newExchange)
                .doOnSuccess(v -> {
                    long endTime = System.currentTimeMillis();
                    // 手动设置MDC
                    MDC.put(HeaderConstants.X_GW_REQUEST_ID, requestId);

                    long durationMs = endTime - startTime;
                    logger.info("Request {} completed, {} {}, in {} s, with status {}", requestId, request.getMethod(),
                            path, durationMs / 1000.0, newExchange.getResponse().getStatusCode());
                    if (!URLUtils.isStreamingPath(path) && !URLUtils.isPollingPath(path)) {
                        nonSseMetrics.recordRequestLatency(path, durationMs);
                    }
                    // 清理MDC
                    MDC.remove(HeaderConstants.X_GW_REQUEST_ID);

                    recordNotAvailableMetrics(path, newExchange);
                    recordFirstTokenStartTimeMetrics(path, newExchange);
                });
    }

    private void recordFirstTokenStartTimeMetrics(String path, ServerWebExchange newExchange) {
        try {
            String firstTokenStartTime = ServerWebExchangeUtils.extractResponseHeader(newExchange, HeaderConstants.HM_RESPONSE_FIRST_TOKEN_START_TIME);
            logger.trace("path {}, firstTokenStartTime:{}", path, firstTokenStartTime);
            if (firstTokenStartTime != null && URLUtils.isPollingPath(path)) {
                long firstTokenStartTimeLong = Long.parseLong(firstTokenStartTime);
                long latencyMs = System.currentTimeMillis() - firstTokenStartTimeLong;
                if (latencyMs > 0) {
                    nonSseMetrics.recordRequestLatency(path, latencyMs);
                    logger.trace("recordFirstTokenStartTimeMetrics path {}, firstTokenStartTime:{}", path, firstTokenStartTime);
                } 
                else {
                    logger.error("recordFirstTokenStartTimeMetrics negative latency {}, path:{}", latencyMs, path);
                }
            }
        } catch (Exception e) {
            logger.error("recordFirstTokenStartTimeMetrics error, path:{}", path, e);
        }
    }

    private void recordNotAvailableMetrics(String path, ServerWebExchange newExchange) {
        String notAvailable = ServerWebExchangeUtils.extractResponseHeader(newExchange, HeaderConstants.HM_RESPONSE_HEADER_NOT_AVAILABLE);
        logger.trace("path {}, notAvailable:{}", path, notAvailable);
        if (Boolean.parseBoolean(notAvailable)) {
            notAvailableMetrics.recordError(path, "notAvailable");
        }
    }

    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE; // 设置优先级，越小优先级越高
    }

}