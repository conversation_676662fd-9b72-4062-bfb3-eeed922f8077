package com.hm.aigcgateway.filter;

import org.slf4j.MDC;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;

import com.hm.aigcgateway.constant.HeaderConstants;
import com.hm.aigcgateway.util.ServerWebExchangeUtils;

import reactor.core.publisher.Mono;

import lombok.extern.slf4j.Slf4j;

/**
 * 响应调试全局过滤器
 * 用于调试所有响应，帮助排查问题
 */
@Component
@Slf4j
public class ResponseDebugGlobalFilter implements GlobalFilter, Ordered {

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        String path = ServerWebExchangeUtils.getAtrrApiOriginalPath(exchange);
        String requestId = ServerWebExchangeUtils.getAttrRequestId(exchange);
        
        MDC.put(HeaderConstants.X_GW_REQUEST_ID, requestId);
        log.debug("请求开始处理: {}", path);

        return chain.filter(exchange)
            .doOnSuccess(v -> {
                ServerHttpResponse response = exchange.getResponse();
                MDC.put(HeaderConstants.X_GW_REQUEST_ID,  requestId);
                log.debug("响应完成: {} {}", path, response.getStatusCode());
            })
            .doOnError(e -> {
                MDC.put(HeaderConstants.X_GW_REQUEST_ID,  requestId);
                log.error("响应异常: {} - {}", path, e.getMessage(), e);
            })
            .doFinally(signal -> {
                MDC.remove(HeaderConstants.X_GW_REQUEST_ID);
            });
    }

    @Override
    public int getOrder() {
        // 比RequestGlobalFilter优先级低一点，确保RequestGlobalFilter先执行
        return Ordered.HIGHEST_PRECEDENCE + 1;
    }
    
}