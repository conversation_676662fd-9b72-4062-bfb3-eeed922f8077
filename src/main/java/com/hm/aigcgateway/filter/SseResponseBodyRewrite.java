// package com.hm.aigcgateway.filter;

// import lombok.extern.slf4j.Slf4j;
// import org.reactivestreams.Publisher;
// import org.springframework.cloud.gateway.filter.factory.rewrite.RewriteFunction;
// import org.springframework.web.server.ServerWebExchange;
// import reactor.core.publisher.Mono;

// /**
//  * SSE响应体重写过滤器，须以RouteLocator编程方式配置使用
//  */
// @Slf4j
// public class SseResponseBodyRewrite implements RewriteFunction<String, String> {

//   @Override
//   public Publisher<String> apply(ServerWebExchange exchange, String body) {
//     // 防止空指针异常
//     if (exchange == null) {
//       log.warn("交换对象exchange为空");
//       return Mono.justOrEmpty(body);
//     }

//     try {
//       // 如果是SSE相关路径，确保设置正确的响应头
//       if (exchange.getRequest() != null) {
//         String path = exchange.getRequest().getURI().getPath();
//         log.debug("处理响应路径: {}", path);

//         if (isStreamingPath(path) && !exchange.getResponse().isCommitted()) {
//           // 添加SSE相关的响应头
//           exchange.getResponse().getHeaders().add("Content-Type", "text/event-stream");
//           exchange.getResponse().getHeaders().add("Cache-Control", "no-cache");
//           exchange.getResponse().getHeaders().add("Connection", "keep-alive");

//           log.debug("SSE响应处理: 已设置响应头");
//         }
//       }
//     } catch (Exception e) {
//       log.warn("设置SSE头失败: {}", e.getMessage(), e);
//     }

//     // 处理空响应体
//     if (body == null) {
//       log.warn("响应体为空");
//       return Mono.just("");
//     }

//     // 返回原始响应体，不做修改
//     return Mono.just(body);
//   }

//   /**
//    * 判断是否为流式路径
//    * 
//    * @param path 请求路径
//    * @return 是否为流式路径
//    */
//   private boolean isStreamingPath(String path) {
//     return path.startsWith("/api/aigc/chat/")
//         || path.startsWith("/api/aigc/sse/") || path.startsWith("/api/aigc/completion/");
//   }

// }