package com.hm.aigcgateway.filter;

import java.util.Arrays;
import java.util.List;

import org.slf4j.MDC;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import com.hm.aigcgateway.constant.HeaderConstants;
import com.hm.aigcgateway.util.ServerWebExchangeUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * SSE头部处理过滤器工厂
 * 专门为SSE请求设置正确的响应头，支持流式传输
 */
@Component
@Slf4j
public class SseResponseHeaderGatewayFilterFactory
        extends AbstractGatewayFilterFactory<SseResponseHeaderGatewayFilterFactory.Config> {

    public SseResponseHeaderGatewayFilterFactory() {
        super(Config.class);
    }

    @Override
    public List<String> shortcutFieldOrder() {
        return Arrays.asList("enabled");
    }

    @Override
    public GatewayFilter apply(Config config) {
        return (exchange, chain) -> {
            if (!config.isEnabled()) {
                return chain.filter(exchange);
            }

            String path = ServerWebExchangeUtils.getAtrrApiOriginalPath(exchange);
            String requestId = ServerWebExchangeUtils.getAttrRequestId(exchange);
            MDC.put(HeaderConstants.X_GW_REQUEST_ID, requestId);

            // 在请求到达下游服务之前，添加处理
            log.trace("SSE响应头部过滤器处理开始: {}", path);

            MDC.remove(HeaderConstants.X_GW_REQUEST_ID);
            return chain.filter(exchange).doOnSuccess(v -> {
                MDC.put(HeaderConstants.X_GW_REQUEST_ID, requestId);
                // 设置响应头，支持SSE
                exchange.getResponse().getHeaders().add(HttpHeaders.CONTENT_TYPE, MediaType.TEXT_EVENT_STREAM_VALUE);
                exchange.getResponse().getHeaders().add(HttpHeaders.CACHE_CONTROL,
                        "no-cache, no-store, max-age=0, must-revalidate");
                exchange.getResponse().getHeaders().add(HttpHeaders.PRAGMA, "no-cache");
                exchange.getResponse().getHeaders().add(HttpHeaders.CONNECTION, "keep-alive");

                // 关键：移除可能干扰流式传输的头
                exchange.getResponse().getHeaders().remove(HttpHeaders.CONTENT_LENGTH);
                exchange.getResponse().getHeaders().remove(HttpHeaders.TRANSFER_ENCODING);

                log.trace("SSE响应头部过滤器处理完成: {}", path);
                MDC.remove(HeaderConstants.X_GW_REQUEST_ID);
            });
        };
    }

    public static class Config {
        private boolean enabled = true;

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }
    }

}