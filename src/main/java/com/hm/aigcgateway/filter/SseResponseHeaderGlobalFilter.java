package com.hm.aigcgateway.filter;

import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;

import com.hm.aigcgateway.constant.HeaderConstants;
import com.hm.aigcgateway.metrics.SseMetrics;
import com.hm.aigcgateway.util.URLUtils;
import com.hm.aigcgateway.util.ServerWebExchangeUtils;

import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

import org.slf4j.MDC;

/**
 * SSE响应处理过滤器
 * 处理流式响应的特殊头部设置
 */
@Component
@Slf4j
public class SseResponseHeaderGlobalFilter implements GlobalFilter, Ordered {

    private final SseMetrics sseMetrics;

    public SseResponseHeaderGlobalFilter(SseMetrics sseMetrics) {
        this.sseMetrics = sseMetrics;
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        return chain.filter(exchange)
            .then(Mono.fromRunnable(() -> {
                String path = ServerWebExchangeUtils.getAtrrApiOriginalPath(exchange);
                String requestId = ServerWebExchangeUtils.getAttrRequestId(exchange);
                Long startTime = ServerWebExchangeUtils.getAttrRequestStartTime(exchange);
                long firstResponseReceivedTimeCost = System.currentTimeMillis() - startTime;
                
                MDC.put(HeaderConstants.X_GW_REQUEST_ID, requestId);
                log.info("准备处理SSE全局响应头: {}, startTime: {}, first response received time cost: {} s", path, startTime, firstResponseReceivedTimeCost / 1000.0);

                if (URLUtils.isStreamingPath(path)) {
                    HttpHeaders headers = exchange.getResponse().getHeaders();
                    
                    // 确保响应状态是200
                    if (exchange.getResponse().getStatusCode() == HttpStatus.OK) {
                        // 设置SSE相关的响应头
                        headers.set(HttpHeaders.CONTENT_TYPE, MediaType.TEXT_EVENT_STREAM_VALUE);
                        headers.set(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, max-age=0, must-revalidate");
                        headers.set(HttpHeaders.PRAGMA, "no-cache");
                        headers.set(HttpHeaders.CONNECTION, "keep-alive");
                        
                        // 确保删除可能影响SSE的头部
                        headers.remove(HttpHeaders.CONTENT_LENGTH);
                        headers.remove(HttpHeaders.TRANSFER_ENCODING);
                        
                        // 记录首包时延指标
                        sseMetrics.recordFirstPacketLatency(path, firstResponseReceivedTimeCost);
                    }
                    log.info("已处理SSE全局响应头: {}, httpCode: {}", path, exchange.getResponse().getStatusCode());
                }
                else {
                    log.debug("非流式请求，不处理SSE全局响应头: {}", path);
                }
                MDC.remove(HeaderConstants.X_GW_REQUEST_ID);
            }));
    }

    @Override
    public int getOrder() {
        // 在响应返回之前执行，但在其他过滤器之后
        return Ordered.LOWEST_PRECEDENCE;
    }

}