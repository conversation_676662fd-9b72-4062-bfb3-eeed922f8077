package com.hm.aigcgateway.metrics;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.DistributionSummary;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;
import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * 非SSE请求指标收集
 */
@Component
@Slf4j
public class NonSseMetrics {

    private final MeterRegistry meterRegistry;

    public NonSseMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
    }

    private static class RequestLatencyEvent {
        private final String url;
        private final long latencyMs;
        public RequestLatencyEvent(String url, long latencyMs) {
            this.url = url;
            this.latencyMs = latencyMs;
        }
        public String getUrl() { return url; }
        public long getLatencyMs() { return latencyMs; }
    }

    private final BlockingQueue<RequestLatencyEvent> latencyQueue = new LinkedBlockingQueue<>(10000);
    private Thread consumerThread;
    private volatile boolean running = true;

    @PostConstruct
    public void init() {
        log.info("NonSseMetrics inited");
        consumerThread = new Thread(() -> {
            while (running || !latencyQueue.isEmpty()) {
                try {
                    RequestLatencyEvent event = latencyQueue.poll(1, TimeUnit.SECONDS);
                    if (event != null) {
                        doRecordRequestLatency(event.getUrl(), event.getLatencyMs());
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.warn("NonSseMetrics consumer thread interrupted");
                } catch (Exception ex) {
                    log.error("Error processing NonSseMetrics event", ex);
                }
            }
            log.info("NonSseMetrics consumer thread stopped");
        }, "non-sse-metrics-consumer");
        consumerThread.setDaemon(true);
        consumerThread.start();
        log.info("NonSseMetrics consumer thread started");
    }

    @PreDestroy
    public void shutdown() {
        running = false;
        if (consumerThread != null) {
            consumerThread.interrupt();
            try {
                consumerThread.join(3000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        log.info("NonSseMetrics shutdown");
    }

    /**
     * 记录非SSE请求时延（异步入队）
     * @param url       API 路径
     * @param latencyMs 时延（毫秒）
     */
    public void recordRequestLatency(String url, long latencyMs) {
        boolean offered = latencyQueue.offer(new RequestLatencyEvent(url, latencyMs));
        if (!offered) {
            log.warn("NonSseMetrics latencyQueue is full, drop metric for url: {}", url);
        }
        log.trace("NonSseMetrics recordRequestLatency: {}, {}", url, latencyMs);
    }

    // 真正的指标记录逻辑
    private void doRecordRequestLatency(String url, long latencyMs) {
        DistributionSummary summary = DistributionSummary.builder("non_sse_request_latency_seconds_histogram")
            .description("非SSE请求时延(按url)Histogram")
            .tag("url", url)
            .tag("type", "latency")
            .serviceLevelObjectives(1, 5, 10, 20, 30, 60, 120, 180, 300)
            .register(meterRegistry);
        summary.record(latencyMs / 1000.0); // 注意单位是秒
    }

} 