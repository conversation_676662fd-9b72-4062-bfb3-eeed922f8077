package com.hm.aigcgateway.metrics;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Counter;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;
import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * URL不可用计数指标收集
 */
@Component
@Slf4j
public class NotAvailableMetrics {

    private final MeterRegistry meterRegistry;

    public NotAvailableMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
    }

    private static class ErrorEvent {
        private final String url;
        private final String errorType; // 可选的错误类型，如timeout、connection_refused等
        
        public ErrorEvent(String url, String errorType) {
            this.url = url;
            this.errorType = errorType;
        }
        
        public String getUrl() { return url; }
        public String getErrorType() { return errorType; }
    }

    private final BlockingQueue<ErrorEvent> errorQueue = new LinkedBlockingQueue<>(10000);
    private Thread consumerThread;
    private volatile boolean running = true;

    @PostConstruct
    public void init() {
        log.info("NotAvailableMetrics inited");
        consumerThread = new Thread(() -> {
            while (running || !errorQueue.isEmpty()) {
                try {
                    ErrorEvent event = errorQueue.poll(1, TimeUnit.SECONDS);
                    if (event != null) {
                        doRecordError(event.getUrl(), event.getErrorType());
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.warn("NotAvailableMetrics consumer thread interrupted");
                } catch (Exception ex) {
                    log.error("Error processing NotAvailableMetrics event", ex);
                }
            }
            log.info("NotAvailableMetrics consumer thread stopped");
        }, "not-available-metrics-consumer");
        consumerThread.setDaemon(true);
        consumerThread.start();
        log.info("NotAvailableMetrics consumer thread started");
    }

    @PreDestroy
    public void shutdown() {
        running = false;
        if (consumerThread != null) {
            consumerThread.interrupt();
            try {
                consumerThread.join(3000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        log.info("NotAvailableMetrics shutdown");
    }

    /**
     * 记录URL错误（异步入队）
     * @param url API路径
     * @param errorType 错误类型（如timeout、connection_refused等）
     */
    public void recordError(String url, String errorType) {
        boolean offered = errorQueue.offer(new ErrorEvent(url, errorType));
        if (!offered) {
            log.warn("NotAvailableMetrics errorQueue is full, drop metric for url: {}, errorType: {}", url, errorType);
        }
        log.trace("NotAvailableMetrics recordError: {}, {}", url, errorType);
    }

    // 真正的指标记录逻辑
    private void doRecordError(String url, String errorType) {
        Counter counter = Counter.builder("service_not_available_total")
            .description("服务不可用计数(按url)")
            .tag("url", url)
            .tag("error_type", errorType)
            .register(meterRegistry);
        counter.increment();
    }
} 