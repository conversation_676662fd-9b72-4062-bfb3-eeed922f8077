package com.hm.aigcgateway.metrics;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.micrometer.core.instrument.DistributionSummary;


import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;
import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * SSE 指标收集
 */
@Component
@Slf4j
public class SseMetrics {

    private final MeterRegistry meterRegistry;

    public SseMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
    }

    private static class FirstPacketLatencyEvent {
        private final String url;
        private final long latencyMs;
        public FirstPacketLatencyEvent(String url, long latencyMs) {
            this.url = url;
            this.latencyMs = latencyMs;
        }
        public String getUrl() { return url; }
        public long getLatencyMs() { return latencyMs; }
    }

    private final BlockingQueue<FirstPacketLatencyEvent> latencyQueue = new LinkedBlockingQueue<>(10000);
    private Thread consumerThread;
    private volatile boolean running = true;

    @PostConstruct
    public void init() {
        log.info("SseMetrics inited");

        consumerThread = new Thread(() -> {
            while (running || !latencyQueue.isEmpty()) {
                try {
                    FirstPacketLatencyEvent event = latencyQueue.poll(1, TimeUnit.SECONDS);
                    if (event != null) {
                        doRecordFirstPacketLatency(event.getUrl(), event.getLatencyMs());
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.warn("SseMetrics consumer thread interrupted");
                } catch (Exception ex) {
                    log.error("Error processing SSE metrics event", ex);
                }
            }
            log.info("SseMetrics consumer thread stopped");
        }, "sse-metrics-consumer");
        consumerThread.setDaemon(true);
        consumerThread.start();

        log.info("SseMetrics consumer thread started");
    }

    @PreDestroy
    public void shutdown() {
        running = false;
        if (consumerThread != null) {
            consumerThread.interrupt();
            try {
                consumerThread.join(3000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        log.info("SseMetrics shutdown");
    }

    /**
     * 记录 SSE 首包时延（异步入队）
     * 
     * @param url       API 路径
     * @param latencyMs 时延（毫秒）
     */
    public void recordFirstPacketLatency(String url, long latencyMs) {
        boolean offered = latencyQueue.offer(new FirstPacketLatencyEvent(url, latencyMs));
        if (!offered) {
            log.warn("SseMetrics latencyQueue is full, drop metric for url: {}", url);
        }
        log.trace("SseMetrics recordFirstPacketLatency: {}, {}", url, latencyMs);
    }

    // 真正的指标记录逻辑
    private void doRecordFirstPacketLatency(String url, long latencyMs) {
        Timer.builder("sse_first_packet_latency_seconds")
            .description("SSE首包时延(按url)")
            .tag("url", url)
            .tag("type", "latency")
            .register(meterRegistry)
            .record(java.time.Duration.ofMillis(latencyMs));

        DistributionSummary summary = DistributionSummary.builder("sse_first_packet_latency_seconds_histogram")
            .description("SSE首包时延(按url)Histogram")
            .tag("url", url)
            .tag("type", "latency")
            .serviceLevelObjectives(1, 5, 10, 20, 30, 60, 120, 180, 300)
            .register(meterRegistry);
        summary.record(latencyMs / 1000.0); // 注意单位是秒
    }

}