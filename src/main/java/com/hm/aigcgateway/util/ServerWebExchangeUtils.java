package com.hm.aigcgateway.util;

import java.util.LinkedHashSet;
import java.util.List;
import java.util.Collections;
import java.util.function.Function;

import org.springframework.http.HttpHeaders;
import org.springframework.web.server.ServerWebExchange;

import com.hm.aigcgateway.constant.ExchangeAttributesConstants;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ServerWebExchangeUtils {

    private static final String GATEWAY_ORIGINAL_REQUEST_URL = "org.springframework.cloud.gateway.support.ServerWebExchangeUtils.gatewayOriginalRequestUrl";

    /**
     * 从URI集合中提取API路径
     */
    public static String extractApiOriginalPath(ServerWebExchange exchange) {
        try {
            LinkedHashSet<java.net.URI> uriSet = exchange.getAttribute(GATEWAY_ORIGINAL_REQUEST_URL);
            if (uriSet == null || uriSet.isEmpty()) {
                return null;
            }
            java.net.URI uri = uriSet.iterator().next();
            return uri.getPath();
        } catch (Exception e) {
            log.error("提取API路径失败", e);
            return null;
        }
    }

    public static String getAtrrApiOriginalPath(ServerWebExchange exchange) {
        return (String) exchange.getAttributes().get(ExchangeAttributesConstants.ATTR_API_ORIGINAL_PATH);
    }

    public static void setAtrrApiOriginalPath(ServerWebExchange exchange, String path) {
        exchange.getAttributes().put(ExchangeAttributesConstants.ATTR_API_ORIGINAL_PATH, path);
    }

    /**
     * 获取请求ID
     */
    public static String getAttrRequestId(ServerWebExchange exchange) {
        return (String) exchange.getAttributes().get(ExchangeAttributesConstants.ATTR_REQUEST_ID);
    }

    /**
     * 设置请求ID
     */
    public static void setAttrRequestId(ServerWebExchange exchange, String requestId) {
        exchange.getAttributes().put(ExchangeAttributesConstants.ATTR_REQUEST_ID, requestId);
    }

    /**
     * 获取请求开始时间
     */
    public static Long getAttrRequestStartTime(ServerWebExchange exchange) {
        return (Long) exchange.getAttributes().get(ExchangeAttributesConstants.ATTR_REQUEST_START_TIME);
    }

    /**
     * 设置请求开始时间
     */
    public static void setAttrRequestStartTime(ServerWebExchange exchange, Long startTime) {
        exchange.getAttributes().put(ExchangeAttributesConstants.ATTR_REQUEST_START_TIME, startTime);
    }

    /**
     * 从响应头中提取指定header的值
     */
    public static String extractResponseHeader(ServerWebExchange exchange, String headerName) {
        return extractHeader(exchange, headerName, "响应头", 
            ex -> ex.getResponse().getHeaders());
    }

    /**
     * 从响应头中提取指定header的所有值
     */
    public static List<String> extractResponseHeaderValues(ServerWebExchange exchange, String headerName) {
        return extractHeaderValues(exchange, headerName, "响应头", 
            ex -> ex.getResponse().getHeaders());
    }

    /**
     * 从请求头中提取指定header的值
     */
    public static String extractRequestHeader(ServerWebExchange exchange, String headerName) {
        return extractHeader(exchange, headerName, "请求头", 
            ex -> ex.getRequest().getHeaders());
    }

    /**
     * 从请求头中提取指定header的所有值
     */
    public static List<String> extractRequestHeaderValues(ServerWebExchange exchange, String headerName) {
        return extractHeaderValues(exchange, headerName, "请求头", 
            ex -> ex.getRequest().getHeaders());
    }

    /**
     * 提取header单值的通用方法
     */
    private static String extractHeader(ServerWebExchange exchange, 
                                     String headerName,
                                     String headerType,
                                     Function<ServerWebExchange, HttpHeaders> headersGetter) {
        try {
            if (exchange == null || headerName == null) {
                return null;
            }
            List<String> headerValues = headersGetter.apply(exchange).get(headerName);
            if (headerValues == null || headerValues.isEmpty()) {
                return null;
            }
            return headerValues.get(0);
        } catch (Exception e) {
            log.error("提取{}[{}]失败", headerType, headerName, e);
            return null;
        }
    }

    /**
     * 提取header多值的通用方法
     */
    private static List<String> extractHeaderValues(ServerWebExchange exchange, 
                                                  String headerName,
                                                  String headerType,
                                                  Function<ServerWebExchange, HttpHeaders> headersGetter) {
        try {
            if (exchange == null || headerName == null) {
                return Collections.emptyList();
            }
            List<String> headerValues = headersGetter.apply(exchange).get(headerName);
            return headerValues != null ? headerValues : Collections.emptyList();
        } catch (Exception e) {
            log.error("提取{}[{}]的所有值失败", headerType, headerName, e);
            return Collections.emptyList();
        }
    }

}
