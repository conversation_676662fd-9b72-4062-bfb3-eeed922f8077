package com.hm.aigcgateway.util;

public class URLUtils {

    /**
     * 判断是否为流式请求路径
     */
    public static boolean isStreamingPath(String path) {
        return path != null && (path.startsWith("/api/aigc/chat/") || path.startsWith("/api/aigc/sse/")
                || path.startsWith("/api/aigc/completion/"));
    }

    /**
     * 判断是否为轮询请求路径
     */
    public static boolean isPollingPath(String path) {
        return path != null && path.startsWith("/api/aigc/recommend/");
    }

}
