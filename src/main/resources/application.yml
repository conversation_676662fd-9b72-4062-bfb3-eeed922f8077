server:
  port: 8580

# logging:
#   config: classpath:log4j2.xml
  # level:
  #   com.hm.aigcgateway: trace

spring:
  application:
    name: aigc-gateway
  cloud:
    gateway:
      discovery:
        locator:
          enabled: true # 启用服务发现
          lower-case-service-id: true # 服务名小写
      # SSE流式接口支持配置
      httpclient:
        response-timeout: 180000 # SSE流需要更长的超时时间（60秒）
        pool:
          max-idle-time: 60000
          acquire-timeout: 60000
      routes:
        # 示例路由配置，可根据实际需求修改
        - id: aigc-service
          uri: lb://aigc-service
          predicates:
            - Path=/api/aigc/recommend/**
          filters:
            - StripPrefix=3
            - name: CircuitBreaker
              args:
                name: aigcServiceCircuitBreaker
                fallbackUri: forward:/fallback
        # SSE流式接口路由配置示例
        - id: aigc-sse-service
          uri: lb://aigc-chat-service
          predicates:
            - Path=/api/aigc/chat/**
          filters:
            - StripPrefix=3
            # 添加SSE头部支持
            - SseResponseHeader
            # 禁用响应缓冲
            - NonBuffering
            - name: CircuitBreaker
              args:
                name: aigcChatCircuitBreaker
                fallbackUri: forward:/fallback
            - RemoveResponseHeader=Content-Length
            - RemoveResponseHeader=Transfer-Encoding
            - RemoveRequestHeader=Connection
            - name: RequestSize
              args:
                maxSize: 5000000
        # HMGPT路由配置 - 支持cdss_stream_chat接口
        - id: hmgpt-service
          uri: lb://hm-gc-business
          predicates:
            - Path=/hmgpt/**
          filters:
            - StripPrefix=1
            # 添加SSE头部支持（如果是流式接口）
            - SseResponseHeader
            # 禁用响应缓冲（如果是流式接口）
            - NonBuffering
            - name: CircuitBreaker
              args:
                name: hmgptServiceCircuitBreaker
                fallbackUri: forward:/fallback
            - RemoveResponseHeader=Content-Length
            - RemoveResponseHeader=Transfer-Encoding
            - RemoveRequestHeader=Connection
            - name: RequestSize
              args:
                maxSize: 5000000
      globalcors:
        corsConfigurations:
          '[/**]':
            allowedOrigins: "*"
            allowedMethods:
              - GET
              - POST
              - PUT
              - DELETE
              - OPTIONS
              - HEAD
              - PATCH
            allowedHeaders:
              - Origin
              - Content-Type
              - Accept
              - Authorization
              - X-Requested-With
              - Access-Control-Request-Method
              - Access-Control-Request-Headers
              - Api-Extend-Params
              - Huimei_id
            allowCredentials: true
            maxAge: 3600
      #暂时不用全局默认过滤器
      default-filters:
        - AddResponseHeader=X-Server-Name,Aigc-Server
        # - SetRequestHeader=X-Request-headerName1, X-Request-headerValue1
        # - MapRequestHeader=X-Request-foo, X-Request-bar
        - DedupeResponseHeader=Access-Control-Allow-Origin Access-Control-Allow-Credentials, RETAIN_FIRST
        # - DedupeResponseHeader=Access-Control-Allow-Origin Access-Control-Allow-Credentials, RETAIN_LAST
        # - DedupeResponseHeader=Access-Control-Allow-Origin Access-Control-Allow-Credentials, RETAIN_UNIQUE
  security:
    user:
      name: admin
      password: KYDFet235R

# 添加Resilience4j配置
resilience4j:
  timelimiter:
    configs:
      default:
        timeoutDuration: 60s  # 默认超时时间改为60秒
      aigcConfig:
        timeoutDuration: 180s  # aigc专用配置3分钟
    instances:
      aigcServiceCircuitBreaker:
        baseConfig: aigcConfig
      aigcChatCircuitBreaker:
        baseConfig: aigcConfig
      hmgptServiceCircuitBreaker:
        baseConfig: aigcConfig
      serviceAppCircuitBreaker:
        timeoutDuration: 10s  # 10秒超时
  circuitbreaker:
    configs:
      default:
        slidingWindowSize: 10
        minimumNumberOfCalls: 5
        permittedNumberOfCallsInHalfOpenState: 3
        automaticTransitionFromOpenToHalfOpenEnabled: true
        waitDurationInOpenState: 5s
    instances:
      aigcServiceCircuitBreaker:
        baseConfig: default
      aigcChatCircuitBreaker:
        baseConfig: default
      hmgptServiceCircuitBreaker:
        baseConfig: default
      serviceAppCircuitBreaker:
        baseConfig: default

eureka:
  client:
    service-url:
      defaultZone: ${aigc-gateway-eureka.url}
    registry-fetch-interval-seconds: 30 # 服务列表刷新间隔
  instance:
    prefer-ip-address: true
    instance-id: ${spring.application.name}:${server.port}:${random.value}
    lease-renewal-interval-in-seconds: 15 # 心跳间隔
    lease-expiration-duration-in-seconds: 30 # 心跳超时

# Actuator配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,prometheus
  endpoint:
    health:
      show-details: always
    prometheus:
      enabled: true
  metrics:
    tags:
      application: ${spring.application.name} 