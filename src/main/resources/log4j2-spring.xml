<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="INFO" monitorInterval="1800">
    <Properties>
        <Property name="LOG_PATTERN">%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{X-GWRequest-ID}] %logger{36} - %msg%n</Property>
        <Property name="LOG_DIR">/hm/logs/aigc-gateway</Property>
        <Property name="LOG_FILE">${LOG_DIR}/aigc-gateway.log</Property>
        <Property name="LOG_FILE_PATTERN">${LOG_DIR}/aigc-gateway-%d{yyyy-MM-dd-HH}-%i.log</Property>
    </Properties>

    <Appenders>
        <!-- Console Appender -->
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="${LOG_PATTERN}"/>
        </Console>

        <!-- Rolling Random Access File Appender -->
        <RollingRandomAccessFile name="AIGC_GATEWAY_FILE_LOG"
                                fileName="${LOG_FILE}"
                                filePattern="${LOG_FILE_PATTERN}">
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1"/>
                <SizeBasedTriggeringPolicy size="10MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingRandomAccessFile>
    </Appenders>

    <Loggers>
        <!-- Root Logger -->
        <Root level="info">
            <AppenderRef ref="AIGC_GATEWAY_FILE_LOG"/>
            <!-- <AppenderRef ref="Console"/> -->
        </Root>

        <!-- Spring Cloud Gateway Logger -->
        <!-- <Logger name="org.springframework.cloud.gateway" level="debug" additivity="false">
            <AppenderRef ref="Console"/>
        </Logger> -->

        <!-- Eureka Client Logger -->
        <!-- <Logger name="com.netflix.discovery" level="debug" additivity="false">
            <AppenderRef ref="Console"/>
        </Logger> -->

        <!-- Circuit Breaker Logger -->
        <!-- <Logger name="org.springframework.cloud.circuitbreaker" level="debug" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="AIGC_GATEWAY_FILE_LOG"/>
        </Logger> -->
    </Loggers>
</Configuration> 