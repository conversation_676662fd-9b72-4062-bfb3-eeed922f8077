package com.hm.aigcgateway.util;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

import java.net.URI;
import java.util.LinkedHashSet;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.http.HttpHeaders;
import org.springframework.mock.http.server.reactive.MockServerHttpResponse;
import org.springframework.mock.web.server.MockServerWebExchange;

public class ServerWebExchangeUtilsTest {

    @Mock
    private ServerWebExchange exchange;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testExtractApiPath_Success() throws Exception {
        // 创建一个URI集合
        LinkedHashSet<URI> uriSet = new LinkedHashSet<>();
        uriSet.add(new URI("http://localhost:8580/api/aigc/chat/test-sse"));
        
        // 模拟exchange返回URI集合
        when(exchange.getAttribute("org.springframework.cloud.gateway.support.ServerWebExchangeUtils.gatewayOriginalRequestUrl"))
                .thenReturn(uriSet);
                
        // 调用方法
        String result = ServerWebExchangeUtils.extractApiOriginalPath(exchange);
        
        // 验证结果
        assertEquals("/api/aigc/chat/test-sse", result);
    }
    
    @Test
    public void testExtractApiPath_EmptySet() {
        // 创建一个空URI集合
        LinkedHashSet<URI> uriSet = new LinkedHashSet<>();
        
        // 模拟exchange返回空URI集合
        when(exchange.getAttribute("org.springframework.cloud.gateway.support.ServerWebExchangeUtils.gatewayOriginalRequestUrl"))
                .thenReturn(uriSet);
                
        // 调用方法
        String result = ServerWebExchangeUtils.extractApiOriginalPath(exchange);
        
        // 验证结果
        assertNull(result);
    }
    
    @Test
    public void testExtractApiPath_NullSet() {
        // 模拟exchange返回null
        when(exchange.getAttribute("org.springframework.cloud.gateway.support.ServerWebExchangeUtils.gatewayOriginalRequestUrl"))
                .thenReturn(null);
                
        // 调用方法
        String result = ServerWebExchangeUtils.extractApiOriginalPath(exchange);
        
        // 验证结果
        assertNull(result);
    }
    
    @Test
    public void testExtractApiPath_Exception() {
        // 模拟exchange在获取属性时抛出异常
        when(exchange.getAttribute("org.springframework.cloud.gateway.support.ServerWebExchangeUtils.gatewayOriginalRequestUrl"))
                .thenThrow(new RuntimeException("模拟异常"));
                
        // 调用方法
        String result = ServerWebExchangeUtils.extractApiOriginalPath(exchange);
        
        // 验证结果
        assertNull(result);
    }

    @Test
    void extractResponseHeader_正常场景_返回第一个值() {
        // Given
        MockServerWebExchange exchange = MockServerWebExchange.from(org.springframework.mock.http.server.reactive.MockServerHttpRequest.get("/test").build());
        exchange.getResponse().getHeaders().add("Content-Type", "application/json");
        exchange.getResponse().getHeaders().add("Content-Type", "text/plain");

        // When
        String result = ServerWebExchangeUtils.extractResponseHeader(exchange, "Content-Type");
        
        // Then
        assertEquals("application/json", result);
    }

    @Test
    void extractResponseHeader_Header不存在_返回Null() {
        // Given
        MockServerWebExchange exchange = MockServerWebExchange.from(org.springframework.mock.http.server.reactive.MockServerHttpRequest.get("/test").build());

        // When
        String result = ServerWebExchangeUtils.extractResponseHeader(exchange, "Non-Existent-Header");

        // Then
        assertNull(result);
    }

    @Test
    void extractResponseHeader_Exchange为Null_返回Null() {
        // When
        String result = ServerWebExchangeUtils.extractResponseHeader(null, "Content-Type");

        // Then
        assertNull(result);
    }

    @Test
    void extractResponseHeader_HeaderName为Null_返回Null() {
        // Given
        MockServerWebExchange exchange = MockServerWebExchange.from(org.springframework.mock.http.server.reactive.MockServerHttpRequest.get("/test").build());

        // When
        String result = ServerWebExchangeUtils.extractResponseHeader(exchange, null);

        // Then
        assertNull(result);
    }

    @Test
    void extractResponseHeaderValues_正常场景_返回所有值() {
        // Given
        MockServerWebExchange exchange = MockServerWebExchange.from(org.springframework.mock.http.server.reactive.MockServerHttpRequest.get("/test").build());
        exchange.getResponse().getHeaders().add("Access-Control-Allow-Origin", "http://localhost:8080");
        exchange.getResponse().getHeaders().add("Access-Control-Allow-Origin", "http://example.com");

        // When
        java.util.List<String> results = ServerWebExchangeUtils.extractResponseHeaderValues(exchange, "Access-Control-Allow-Origin");

        // Then
        assertEquals(2, results.size());
        assertEquals("http://localhost:8080", results.get(0));
        assertEquals("http://example.com", results.get(1));
    }

    @Test
    void extractResponseHeaderValues_Header不存在_返回空列表() {
        // Given
        MockServerWebExchange exchange = MockServerWebExchange.from(org.springframework.mock.http.server.reactive.MockServerHttpRequest.get("/test").build());

        // When
        java.util.List<String> results = ServerWebExchangeUtils.extractResponseHeaderValues(exchange, "Non-Existent-Header");

        // Then
        assertTrue(results.isEmpty());
    }

    @Test
    void extractResponseHeaderValues_Exchange为Null_返回空列表() {
        // When
        java.util.List<String> results = ServerWebExchangeUtils.extractResponseHeaderValues(null, "Content-Type");

        // Then
        assertTrue(results.isEmpty());
    }

    @Test
    void extractResponseHeaderValues_HeaderName为Null_返回空列表() {
        // Given
        MockServerWebExchange exchange = MockServerWebExchange.from(org.springframework.mock.http.server.reactive.MockServerHttpRequest.get("/test").build());

        // When
        java.util.List<String> results = ServerWebExchangeUtils.extractResponseHeaderValues(exchange, null);

        // Then
        assertTrue(results.isEmpty());
    }

} 